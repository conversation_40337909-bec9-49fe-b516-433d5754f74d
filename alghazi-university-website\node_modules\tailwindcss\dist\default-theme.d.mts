import { P as PluginUtils, N as NamedUtilityValue } from './resolve-config-QUZ9b-Gn.mjs';
import './colors.mjs';

declare const _default: {
    accentColor: ({ theme }: PluginUtils) => any;
    animation: {
        none: string;
        spin: string;
        ping: string;
        pulse: string;
        bounce: string;
    };
    aria: {
        busy: string;
        checked: string;
        disabled: string;
        expanded: string;
        hidden: string;
        pressed: string;
        readonly: string;
        required: string;
        selected: string;
    };
    aspectRatio: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        auto: string;
        square: string;
        video: string;
    };
    backdropBlur: ({ theme }: PluginUtils) => any;
    backdropBrightness: ({ theme }: PluginUtils) => any;
    backdropContrast: ({ theme }: PluginUtils) => any;
    backdropGrayscale: ({ theme }: PluginUtils) => any;
    backdropHueRotate: ({ theme }: PluginUtils) => any;
    backdropInvert: ({ theme }: PluginUtils) => any;
    backdropOpacity: ({ theme }: PluginUtils) => any;
    backdropSaturate: ({ theme }: PluginUtils) => any;
    backdropSepia: ({ theme }: PluginUtils) => any;
    backgroundColor: ({ theme }: PluginUtils) => any;
    backgroundImage: {
        none: string;
        'gradient-to-t': string;
        'gradient-to-tr': string;
        'gradient-to-r': string;
        'gradient-to-br': string;
        'gradient-to-b': string;
        'gradient-to-bl': string;
        'gradient-to-l': string;
        'gradient-to-tl': string;
    };
    backgroundOpacity: ({ theme }: PluginUtils) => any;
    backgroundPosition: {
        bottom: string;
        center: string;
        left: string;
        'left-bottom': string;
        'left-top': string;
        right: string;
        'right-bottom': string;
        'right-top': string;
        top: string;
    };
    backgroundSize: {
        auto: string;
        cover: string;
        contain: string;
    };
    blur: {
        0: string;
        none: string;
        sm: string;
        DEFAULT: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
        '3xl': string;
    };
    borderColor: ({ theme }: PluginUtils) => any;
    borderOpacity: ({ theme }: PluginUtils) => any;
    borderRadius: {
        none: string;
        sm: string;
        DEFAULT: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
        '3xl': string;
        full: string;
    };
    borderSpacing: ({ theme }: PluginUtils) => any;
    borderWidth: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        DEFAULT: string;
        0: string;
        2: string;
        4: string;
        8: string;
    };
    boxShadow: {
        sm: string;
        DEFAULT: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
        inner: string;
        none: string;
    };
    boxShadowColor: ({ theme }: PluginUtils) => any;
    brightness: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        50: string;
        75: string;
        90: string;
        95: string;
        100: string;
        105: string;
        110: string;
        125: string;
        150: string;
        200: string;
    };
    caretColor: ({ theme }: PluginUtils) => any;
    colors: () => {
        inherit: string;
        current: string;
        transparent: string;
        black: string;
        white: string;
        slate: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        gray: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        zinc: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        neutral: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        stone: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        red: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        orange: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        amber: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        yellow: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        lime: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        green: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        emerald: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        teal: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        cyan: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        sky: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        blue: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        indigo: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        violet: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        purple: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        fuchsia: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        pink: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
        rose: {
            '50': string;
            '100': string;
            '200': string;
            '300': string;
            '400': string;
            '500': string;
            '600': string;
            '700': string;
            '800': string;
            '900': string;
            '950': string;
        };
    };
    columns: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        auto: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        11: string;
        12: string;
        '3xs': string;
        '2xs': string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
        '3xl': string;
        '4xl': string;
        '5xl': string;
        '6xl': string;
        '7xl': string;
    };
    container: {};
    content: {
        none: string;
    };
    contrast: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        50: string;
        75: string;
        100: string;
        125: string;
        150: string;
        200: string;
    };
    cursor: {
        auto: string;
        default: string;
        pointer: string;
        wait: string;
        text: string;
        move: string;
        help: string;
        'not-allowed': string;
        none: string;
        'context-menu': string;
        progress: string;
        cell: string;
        crosshair: string;
        'vertical-text': string;
        alias: string;
        copy: string;
        'no-drop': string;
        grab: string;
        grabbing: string;
        'all-scroll': string;
        'col-resize': string;
        'row-resize': string;
        'n-resize': string;
        'e-resize': string;
        's-resize': string;
        'w-resize': string;
        'ne-resize': string;
        'nw-resize': string;
        'se-resize': string;
        'sw-resize': string;
        'ew-resize': string;
        'ns-resize': string;
        'nesw-resize': string;
        'nwse-resize': string;
        'zoom-in': string;
        'zoom-out': string;
    };
    divideColor: ({ theme }: PluginUtils) => any;
    divideOpacity: ({ theme }: PluginUtils) => any;
    divideWidth: ({ theme }: PluginUtils) => any;
    dropShadow: {
        sm: string;
        DEFAULT: string[];
        md: string[];
        lg: string[];
        xl: string[];
        '2xl': string;
        none: string;
    };
    fill: ({ theme }: PluginUtils) => any;
    flex: {
        1: string;
        auto: string;
        initial: string;
        none: string;
    };
    flexBasis: ({ theme }: PluginUtils) => any;
    flexGrow: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        DEFAULT: string;
    };
    flexShrink: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        DEFAULT: string;
    };
    fontFamily: {
        sans: string[];
        serif: string[];
        mono: string[];
    };
    fontSize: {
        xs: (string | {
            lineHeight: string;
        })[];
        sm: (string | {
            lineHeight: string;
        })[];
        base: (string | {
            lineHeight: string;
        })[];
        lg: (string | {
            lineHeight: string;
        })[];
        xl: (string | {
            lineHeight: string;
        })[];
        '2xl': (string | {
            lineHeight: string;
        })[];
        '3xl': (string | {
            lineHeight: string;
        })[];
        '4xl': (string | {
            lineHeight: string;
        })[];
        '5xl': (string | {
            lineHeight: string;
        })[];
        '6xl': (string | {
            lineHeight: string;
        })[];
        '7xl': (string | {
            lineHeight: string;
        })[];
        '8xl': (string | {
            lineHeight: string;
        })[];
        '9xl': (string | {
            lineHeight: string;
        })[];
    };
    fontWeight: {
        thin: string;
        extralight: string;
        light: string;
        normal: string;
        medium: string;
        semibold: string;
        bold: string;
        extrabold: string;
        black: string;
    };
    gap: ({ theme }: PluginUtils) => any;
    gradientColorStops: ({ theme }: PluginUtils) => any;
    gradientColorStopPositions: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        '0%': string;
        '5%': string;
        '10%': string;
        '15%': string;
        '20%': string;
        '25%': string;
        '30%': string;
        '35%': string;
        '40%': string;
        '45%': string;
        '50%': string;
        '55%': string;
        '60%': string;
        '65%': string;
        '70%': string;
        '75%': string;
        '80%': string;
        '85%': string;
        '90%': string;
        '95%': string;
        '100%': string;
    };
    grayscale: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        DEFAULT: string;
    };
    gridAutoColumns: {
        auto: string;
        min: string;
        max: string;
        fr: string;
    };
    gridAutoRows: {
        auto: string;
        min: string;
        max: string;
        fr: string;
    };
    gridColumn: {
        auto: string;
        'span-1': string;
        'span-2': string;
        'span-3': string;
        'span-4': string;
        'span-5': string;
        'span-6': string;
        'span-7': string;
        'span-8': string;
        'span-9': string;
        'span-10': string;
        'span-11': string;
        'span-12': string;
        'span-full': string;
    };
    gridColumnEnd: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        auto: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        11: string;
        12: string;
        13: string;
    };
    gridColumnStart: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        auto: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        11: string;
        12: string;
        13: string;
    };
    gridRow: {
        auto: string;
        'span-1': string;
        'span-2': string;
        'span-3': string;
        'span-4': string;
        'span-5': string;
        'span-6': string;
        'span-7': string;
        'span-8': string;
        'span-9': string;
        'span-10': string;
        'span-11': string;
        'span-12': string;
        'span-full': string;
    };
    gridRowEnd: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        auto: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        11: string;
        12: string;
        13: string;
    };
    gridRowStart: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        auto: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        11: string;
        12: string;
        13: string;
    };
    gridTemplateColumns: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        none: string;
        subgrid: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        11: string;
        12: string;
    };
    gridTemplateRows: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        none: string;
        subgrid: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        11: string;
        12: string;
    };
    height: ({ theme }: PluginUtils) => any;
    hueRotate: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        15: string;
        30: string;
        60: string;
        90: string;
        180: string;
    };
    inset: ({ theme }: PluginUtils) => any;
    invert: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        DEFAULT: string;
    };
    keyframes: {
        spin: {
            to: {
                transform: string;
            };
        };
        ping: {
            '75%, 100%': {
                transform: string;
                opacity: string;
            };
        };
        pulse: {
            '50%': {
                opacity: string;
            };
        };
        bounce: {
            '0%, 100%': {
                transform: string;
                animationTimingFunction: string;
            };
            '50%': {
                transform: string;
                animationTimingFunction: string;
            };
        };
    };
    letterSpacing: {
        tighter: string;
        tight: string;
        normal: string;
        wide: string;
        wider: string;
        widest: string;
    };
    lineHeight: {
        none: string;
        tight: string;
        snug: string;
        normal: string;
        relaxed: string;
        loose: string;
        3: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
    };
    listStyleType: {
        none: string;
        disc: string;
        decimal: string;
    };
    listStyleImage: {
        none: string;
    };
    margin: ({ theme }: PluginUtils) => any;
    lineClamp: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        6: string;
    };
    maxHeight: ({ theme }: PluginUtils) => any;
    maxWidth: ({ theme }: PluginUtils) => any;
    minHeight: ({ theme }: PluginUtils) => any;
    minWidth: ({ theme }: PluginUtils) => any;
    objectPosition: {
        bottom: string;
        center: string;
        left: string;
        'left-bottom': string;
        'left-top': string;
        right: string;
        'right-bottom': string;
        'right-top': string;
        top: string;
    };
    opacity: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        5: string;
        10: string;
        15: string;
        20: string;
        25: string;
        30: string;
        35: string;
        40: string;
        45: string;
        50: string;
        55: string;
        60: string;
        65: string;
        70: string;
        75: string;
        80: string;
        85: string;
        90: string;
        95: string;
        100: string;
    };
    order: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        first: string;
        last: string;
        none: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        11: string;
        12: string;
    };
    outlineColor: ({ theme }: PluginUtils) => any;
    outlineOffset: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        1: string;
        2: string;
        4: string;
        8: string;
    };
    outlineWidth: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        1: string;
        2: string;
        4: string;
        8: string;
    };
    padding: ({ theme }: PluginUtils) => any;
    placeholderColor: ({ theme }: PluginUtils) => any;
    placeholderOpacity: ({ theme }: PluginUtils) => any;
    ringColor: ({ theme }: PluginUtils) => any;
    ringOffsetColor: ({ theme }: PluginUtils) => any;
    ringOffsetWidth: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        1: string;
        2: string;
        4: string;
        8: string;
    };
    ringOpacity: ({ theme }: PluginUtils) => any;
    ringWidth: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        DEFAULT: string;
        0: string;
        1: string;
        2: string;
        4: string;
        8: string;
    };
    rotate: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        1: string;
        2: string;
        3: string;
        6: string;
        12: string;
        45: string;
        90: string;
        180: string;
    };
    saturate: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        50: string;
        100: string;
        150: string;
        200: string;
    };
    scale: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        50: string;
        75: string;
        90: string;
        95: string;
        100: string;
        105: string;
        110: string;
        125: string;
        150: string;
    };
    screens: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
    };
    scrollMargin: ({ theme }: PluginUtils) => any;
    scrollPadding: ({ theme }: PluginUtils) => any;
    sepia: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        DEFAULT: string;
    };
    skew: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        1: string;
        2: string;
        3: string;
        6: string;
        12: string;
    };
    space: ({ theme }: PluginUtils) => any;
    spacing: {
        px: string;
        0: string;
        0.5: string;
        1: string;
        1.5: string;
        2: string;
        2.5: string;
        3: string;
        3.5: string;
        4: string;
        5: string;
        6: string;
        7: string;
        8: string;
        9: string;
        10: string;
        11: string;
        12: string;
        14: string;
        16: string;
        20: string;
        24: string;
        28: string;
        32: string;
        36: string;
        40: string;
        44: string;
        48: string;
        52: string;
        56: string;
        60: string;
        64: string;
        72: string;
        80: string;
        96: string;
    };
    stroke: ({ theme }: PluginUtils) => any;
    strokeWidth: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        1: string;
        2: string;
    };
    supports: {};
    data: {};
    textColor: ({ theme }: PluginUtils) => any;
    textDecorationColor: ({ theme }: PluginUtils) => any;
    textDecorationThickness: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        auto: string;
        'from-font': string;
        0: string;
        1: string;
        2: string;
        4: string;
        8: string;
    };
    textIndent: ({ theme }: PluginUtils) => any;
    textOpacity: ({ theme }: PluginUtils) => any;
    textUnderlineOffset: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        auto: string;
        0: string;
        1: string;
        2: string;
        4: string;
        8: string;
    };
    transformOrigin: {
        center: string;
        top: string;
        'top-right': string;
        right: string;
        'bottom-right': string;
        bottom: string;
        'bottom-left': string;
        left: string;
        'top-left': string;
    };
    transitionDelay: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        0: string;
        75: string;
        100: string;
        150: string;
        200: string;
        300: string;
        500: string;
        700: string;
        1000: string;
    };
    transitionDuration: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        DEFAULT: string;
        0: string;
        75: string;
        100: string;
        150: string;
        200: string;
        300: string;
        500: string;
        700: string;
        1000: string;
    };
    transitionProperty: {
        none: string;
        all: string;
        DEFAULT: string;
        colors: string;
        opacity: string;
        shadow: string;
        transform: string;
    };
    transitionTimingFunction: {
        DEFAULT: string;
        linear: string;
        in: string;
        out: string;
        'in-out': string;
    };
    translate: ({ theme }: PluginUtils) => any;
    size: ({ theme }: PluginUtils) => any;
    width: ({ theme }: PluginUtils) => any;
    willChange: {
        auto: string;
        scroll: string;
        contents: string;
        transform: string;
    };
    zIndex: {
        __BARE_VALUE__: (value: NamedUtilityValue) => string | undefined;
        auto: string;
        0: string;
        10: string;
        20: string;
        30: string;
        40: string;
        50: string;
    };
};

export { _default as default };
